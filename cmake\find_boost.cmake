include(FetchContent)
set(BOOST_VERSION_MAJOR 1)
if(BUILD_ON_NX)
  set(BOOST_VERSION_MINOR 77)
else()
  set(BOOST_VERSION_MINOR 74)
endif()
set(BOOST_VERSION_PATCH 0)

if(BUILD_ON_NX)
  set(BOOST_PLATFORM linux-gcc7-arm64)
else()
  set(BOOST_PLATFORM ${import_platform})
endif()

set(lib_boost_resource_path ${AIDI_DEPS_ROOT}/boost/comp-boost-${BOOST_VERSION_MAJOR}.${BOOST_VERSION_MINOR}.${BOOST_VERSION_PATCH}-${BOOST_PLATFORM}.${compressed_lib_suffix})


FetchContent_Declare(
  boost
  URL  ${lib_boost_resource_path}
  UPDATE_DISCONNECTED
)

FetchContent_GetProperties(boost)

if(NOT boost_POPULATED)
    FetchContent_Populate(boost)
    if(WIN32)
        set(_boost_location_release_dir bin)
        set(_boost_location_debug_dir bin)
        set(_boost_lib_release_dir build/Release)
        set(_boost_lib_debug_dir build/Debug)
        set(_boost_location_release_suffix -vc141-mt-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.${import_shared_lib_suffix})
        set(_boost_location_debug_suffix -vc141-mt-gd-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.${import_shared_lib_suffix})
        set(_boost_lib_release_suffix -vc141-mt-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.${import_static_lib_suffix})
        set(_boost_lib_debug_suffix -vc141-mt-gd-x64-${BOOST_VERSION_MAJOR}_${BOOST_VERSION_MINOR}.${import_static_lib_suffix})
    else()
        if(BUILD_ON_NX)
          set(_boost_location_release_dir lib)
          set(_boost_location_debug_dir lib)
          set(_boost_location_release_suffix .${import_static_lib_suffix})
          set(_boost_location_debug_suffix .${import_static_lib_suffix})
        else()
          set(_boost_location_release_dir lib/Release)
          set(_boost_location_debug_dir lib/Debug)
          set(_boost_lib_release_dir lib/Release)
          set(_boost_lib_debug_dir lib/Debug)
          set(_boost_location_release_suffix .${import_shared_lib_suffix}.${BOOST_VERSION_MAJOR}.${BOOST_VERSION_MINOR}.${BOOST_VERSION_PATCH})
          set(_boost_location_debug_suffix .${import_shared_lib_suffix}.${BOOST_VERSION_MAJOR}.${BOOST_VERSION_MINOR}.${BOOST_VERSION_PATCH})
          set(_boost_lib_release_suffix .${import_static_lib_suffix}.${BOOST_VERSION_MAJOR}.${BOOST_VERSION_MINOR}.${BOOST_VERSION_PATCH})
          set(_boost_lib_debug_suffix .${import_static_lib_suffix}.${BOOST_VERSION_MAJOR}.${BOOST_VERSION_MINOR}.${BOOST_VERSION_PATCH})
        endif()
    endif()


    set(boost_modules system regex log log_setup locale filesystem date_time chrono atomic)


    foreach(mod ${boost_modules})
     
      if(WIN32)
         add_library(Boost::${mod} SHARED IMPORTED)
         set_target_properties(
          Boost::${mod}
          PROPERTIES
          IMPORTED_LOCATION
          ${boost_SOURCE_DIR}/${_boost_location_release_dir}/${import_lib_prefix}boost_${mod}${_boost_location_release_suffix}
          IMPORTED_LOCATION_DEBUG
          ${boost_SOURCE_DIR}/${_boost_location_debug_dir}/${import_lib_prefix}boost_${mod}${_boost_location_debug_suffix}
          IMPORTED_IMPLIB
          ${boost_SOURCE_DIR}/${_boost_lib_release_dir}/${import_lib_prefix}boost_${mod}${_boost_lib_release_suffix}
          IMPORTED_IMPLIB_DEBUG
          ${boost_SOURCE_DIR}/${_boost_lib_debug_dir}/${import_lib_prefix}boost_${mod}${_boost_lib_debug_suffix}
          INTERFACE_INCLUDE_DIRECTORIES
          ${boost_SOURCE_DIR}/include
          INTERFACE_COMPILE_DEFINITIONS
          BOOST_ALL_DYN_LINK
        )
      else()
        if(BUILD_ON_NX)
            add_library(Boost::${mod} STATIC IMPORTED)
            set_target_properties(
            Boost::${mod}
            PROPERTIES
            IMPORTED_LOCATION
            ${boost_SOURCE_DIR}/${_boost_location_release_dir}/${import_lib_prefix}boost_${mod}${_boost_location_release_suffix}
            IMPORTED_LOCATION_DEBUG
            ${boost_SOURCE_DIR}/${_boost_location_debug_dir}/${import_lib_prefix}boost_${mod}${_boost_location_debug_suffix}
            INTERFACE_INCLUDE_DIRECTORIES
            ${boost_SOURCE_DIR}/include
            INTERFACE_COMPILE_DEFINITIONS
            Boost_USE_STATIC_LIBS
            )
        else()
            add_library(Boost::${mod} SHARED IMPORTED)
            set_target_properties(
            Boost::${mod}
            PROPERTIES
            IMPORTED_LOCATION
            ${boost_SOURCE_DIR}/${_boost_location_release_dir}/${import_lib_prefix}boost_${mod}${_boost_location_release_suffix}
            IMPORTED_LOCATION_DEBUG
            ${boost_SOURCE_DIR}/${_boost_location_debug_dir}/${import_lib_prefix}boost_${mod}${_boost_location_debug_suffix}
            INTERFACE_INCLUDE_DIRECTORIES
            ${boost_SOURCE_DIR}/include
            INTERFACE_COMPILE_DEFINITIONS
            BOOST_ALL_DYN_LINK
          )
        endif()
      endif()
      if(NOT BUILD_ON_NX)
        install(
          FILES $<TARGET_FILE:Boost::${mod}>
          DESTINATION ${export_librarys_directory}
        )
      endif()

    endforeach(mod)

    # To use some features of thread interruption, change the version of Boost.Thread to 3
    
  if(WIN32)
    add_library(Boost::thread SHARED IMPORTED)
    set_target_properties(
     Boost::thread
     PROPERTIES
     IMPORTED_LOCATION
     ${boost_SOURCE_DIR}/${_boost_location_release_dir}/${import_lib_prefix}boost_thread${_boost_location_release_suffix}
     IMPORTED_LOCATION_DEBUG
     ${boost_SOURCE_DIR}/${_boost_location_debug_dir}/${import_lib_prefix}boost_thread${_boost_location_debug_suffix}
     IMPORTED_IMPLIB
     ${boost_SOURCE_DIR}/${_boost_lib_release_dir}/${import_lib_prefix}boost_thread${_boost_lib_release_suffix}
     IMPORTED_IMPLIB_DEBUG
     ${boost_SOURCE_DIR}/${_boost_lib_debug_dir}/${import_lib_prefix}boost_thread${_boost_lib_debug_suffix}
     INTERFACE_INCLUDE_DIRECTORIES
     ${boost_SOURCE_DIR}/include
     INTERFACE_COMPILE_DEFINITIONS
     BOOST_ALL_DYN_LINK
     INTERFACE_COMPILE_DEFINITIONS
     BOOST_THREAD_VERSION=3
   )
 else()
   if(BUILD_ON_NX)
       add_library(Boost::thread STATIC IMPORTED)
       set_target_properties(
       Boost::thread
       PROPERTIES
       IMPORTED_LOCATION
       ${boost_SOURCE_DIR}/${_boost_location_release_dir}/${import_lib_prefix}boost_thread${_boost_location_release_suffix}
       IMPORTED_LOCATION_DEBUG
       ${boost_SOURCE_DIR}/${_boost_location_debug_dir}/${import_lib_prefix}boost_thread${_boost_location_debug_suffix}
       INTERFACE_INCLUDE_DIRECTORIES
       ${boost_SOURCE_DIR}/include
       INTERFACE_COMPILE_DEFINITIONS
       Boost_USE_STATIC_LIBS
       INTERFACE_COMPILE_DEFINITIONS
       BOOST_THREAD_VERSION=3
       )
   else()
       add_library(Boost::thread SHARED IMPORTED)
       set_target_properties(
       Boost::thread
       PROPERTIES
       IMPORTED_LOCATION
       ${boost_SOURCE_DIR}/${_boost_location_release_dir}/${import_lib_prefix}boost_thread${_boost_location_release_suffix}
       IMPORTED_LOCATION_DEBUG
       ${boost_SOURCE_DIR}/${_boost_location_debug_dir}/${import_lib_prefix}boost_thread${_boost_location_debug_suffix}
       INTERFACE_INCLUDE_DIRECTORIES
       ${boost_SOURCE_DIR}/include
       INTERFACE_COMPILE_DEFINITIONS
       BOOST_ALL_DYN_LINK
       INTERFACE_COMPILE_DEFINITIONS
       BOOST_THREAD_VERSION=3
     )
   endif()
 endif()

    
  if(NOT BUILD_ON_NX)
      install(
        FILES $<TARGET_FILE:Boost::thread>
        DESTINATION ${export_librarys_directory}
      )
  endif()

endif()
