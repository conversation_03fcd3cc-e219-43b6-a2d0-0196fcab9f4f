﻿set(VERSION 420)

set(CUDA_VERSION 11.1)

set(PROTOC protoc)

# import/export def in win
set(EXPORT_MICRO "")
set(IMPORT_MICRO "")

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fopenmp -fPIC")
set(CMAKE_C_FLAGS "${CMAKE_CXX_FLAGS} -fopenmp -fPIC")
set(CMAKE_CUDA_FLAGS "${CMAKE_CUDA_FLAGS} -fopenmp -fPIC")

set(GOMP_LIB gomp)

set(SYSTEM_DEPS GOMP_LIB)

set(export_librarys_directory "lib")

set(import_lib_prefix "lib")

set(import_shared_lib_suffix "so")
set(import_static_lib_suffix "a")

if(BUILD_ON_NX)
    set(import_platform "linux-gcc5-arm64")
else()
    set(import_platform "linux-gcc5-x64")
    set(SWIG ${SWIG_PATH}/swig)
endif()

set(compressed_lib_suffix "tar.gz")
