cmake_minimum_required(VERSION 3.19)

project(AiVideoCore VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_BUILD_TYPE Release)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
set(VCPKG_ROOT "D:/yupei.wu/vcpkg" CACHE PATH "Vcpkg root directory" FORCE)
set(CMAKE_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake" CACHE STRING "Vcpkg toolchain file" FORCE)
include(${CMAKE_TOOLCHAIN_FILE})
# Ensure standard library headers are found
include_directories(SYSTEM "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/include")
# use only to install pybind11
# set(ENV{http_proxy} "http://*************:7890")
# set(ENV{https_proxy} "http://*************:7890")

set(CMAKE_INSTALL_PREFIX ${CMAKE_SOURCE_DIR}/bin)

add_definitions("-DUNICODE" "-D_UNICODE")

add_compile_options(
    /Zi
    /Od
    /utf-8     # 强制使用 UTF-8 编码
    /wd4819    # 禁用 code page 警告
)
add_link_options(/DEBUG)

foreach(_ts_file ${TS_FILES})
    execute_process(
        COMMAND ${LUPDATE_EXECUTABLE} -recursive ${CMAKE_SOURCE_DIR} -ts ${_ts_file})
    execute_process(
        COMMAND ${LRELEASE_EXECUTABLE} ${_ts_file})
endforeach()

# 查找 Python 组件
find_package(Python3 3.12 EXACT COMPONENTS Interpreter Development REQUIRED)
if(NOT Python3_FOUND)
    message(FATAL_ERROR "Python3 not found!")
endif()

include(cmake/parse_config.cmake)

if (WIN32)
    include(cmake/setting-win.cmake)
elseif(UNIX)
    include(cmake/setting-unix.cmake)
endif()

include(cmake/find_boost.cmake)
include(cmake/find_visionflow.cmake)
include(cmake/find_pybind11.cmake)

# 自动检测并安装vcpkg依赖的DLL
if(WIN32)
    # 获取当前构建配置（Debug/Release等）
    set(VCPKG_BINARY_DIR "${VCPKG_ROOT}/installed/${VCPKG_TARGET_TRIPLET}/bin")
    message(STATUS "VCPKG_BINARY_DIR: ${VCPKG_BINARY_DIR}")
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(VCPKG_BINARY_DIR "${VCPKG_ROOT}/installed/${VCPKG_TARGET_TRIPLET}/debug/bin")
    endif()
    
    # 查找所有依赖的DLL文件
    file(GLOB_RECURSE DEPENDENCY_DLLS 
        LIST_DIRECTORIES false
        "${VCPKG_BINARY_DIR}/*.dll"
    )
    message(STATUS "Found vcpkg dependencies: ${DEPENDENCY_DLLS}")
    
    # 将DLL安装到bin目录
    install(
        FILES ${DEPENDENCY_DLLS}
        DESTINATION release
        CONFIGURATIONS ${CMAKE_BUILD_TYPE}
    )
endif()

add_subdirectory(AiVideoCore)
add_subdirectory(examples)
add_subdirectory(plugins/task)

# 导出配置
include(CMakePackageConfigHelpers)

# 创建版本文件
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/AiVideoCoreConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)

# 创建配置文件
configure_file(cmake/AiVideoCoreConfig.cmake.in
    "${CMAKE_CURRENT_BINARY_DIR}/AiVideoCoreConfig.cmake"
    @ONLY
)

# 安装配置文件
install(
    FILES
        "${CMAKE_CURRENT_BINARY_DIR}/AiVideoCoreConfig.cmake"
        "${CMAKE_CURRENT_BINARY_DIR}/AiVideoCoreConfigVersion.cmake"
    DESTINATION lib/cmake/AiVideoCore
)



