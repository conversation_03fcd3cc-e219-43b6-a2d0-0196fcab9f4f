﻿file(STRINGS ${CMAKE_SOURCE_DIR}/.config.conf conf)

foreach(_line ${conf})
    string(FIND ${_line} " " _pos)
    string(LENGTH ${_line} _len)
    string(SUBSTRING ${_line} 0 ${_pos} _left)
    math(EXPR _pos "${_pos} + 1")
    math(EXPR _remain "${_len} - ${_pos}")
    string(SUBSTRING ${_line} ${_pos} ${_remain} _right)
    if (_left STREQUAL "AIDI_DEPS_ROOT")
        set(AIDI_DEPS_ROOT ${_right})
    endif()
endforeach()

if (NOT AIDI_DEPS_ROOT)
    message(FATAL_ERROR "AIDI_DEPS_ROOT is empty.")
endif()
