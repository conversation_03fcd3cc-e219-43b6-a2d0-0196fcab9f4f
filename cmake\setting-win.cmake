﻿
set(CUDA_VERSION 11.1)

set(SWIG ${SWIG_PATH}/swig.exe)

set(DOXYGEN ${DOXYGEN_PATH}/doxygen.exe)

# import/export def in win
set(EXPORT_MICRO "__declspec(dllexport)")
set(IMPORT_MICRO "__declspec(dllimport)")

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Zm800 /openmp /MP4 /wd4661 /utf-8")

set(SYSTEM_LIB
    user32 shell32 crypt32 Normaliz
    Shlwapi wsock32 iphlpapi urlmon
    bcrypt
)

set(export_librarys_directory "bin")

set(import_lib_prefix "")

set(import_shared_lib_suffix "dll")
set(import_static_lib_suffix "lib")

set(import_platform "win-vc141-x64")

set(compressed_lib_suffix "zip")